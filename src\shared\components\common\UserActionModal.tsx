import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, Form, FormItem, Textarea, Button, Typography } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';

interface UserActionModalProps {
  /**
   * Hiển thị modal
   */
  isOpen: boolean;

  /**
   * Loại hành động: 'block' hoặc 'unblock'
   */
  actionType: 'block' | 'unblock';

  /**
   * Tên người dùng
   */
  userName: string;

  /**
   * Đang xử lý
   */
  isSubmitting?: boolean;

  /**
   * Callback khi đóng modal
   */
  onClose: () => void;

  /**
   * Callback khi xác nhận với lý do
   */
  onConfirm: (reason: string) => void;
}



/**
 * Modal để nhập lý do khóa/mở khóa tài khoản người dùng
 */
const UserActionModal: React.FC<UserActionModalProps> = ({
  isOpen,
  actionType,
  userName,
  isSubmitting = false,
  onClose,
  onConfirm,
}) => {
  const { t } = useTranslation(['user', 'common']);
  const formRef = useRef<FormRef<any>>(null);

  // Reset form khi modal đóng/mở
  React.useEffect(() => {
    if (isOpen) {
      formRef.current?.reset({ reason: '' });
      formRef.current?.setErrors({});
    }
  }, [isOpen]);

  // Xử lý submit form
  const handleSubmit = (data: any) => {
    // Validate
    const errors: Record<string, string> = {};
    if (!data['reason']?.trim()) {
      errors['reason'] = t('user:actions.reasonRequired', 'Vui lòng nhập lý do');
    } else if (data['reason'].trim().length < 10) {
      errors['reason'] = t('user:actions.reasonTooShort', 'Lý do phải có ít nhất 10 ký tự');
    }

    if (Object.keys(errors).length > 0) {
      formRef.current?.setErrors(errors);
      return;
    }

    // Gọi callback với lý do
    onConfirm(data['reason'].trim());
  };

  // Xử lý hủy
  const handleCancel = () => {
    formRef.current?.reset({ reason: '' });
    formRef.current?.setErrors({});
    onClose();
  };

  // Tiêu đề và nội dung theo loại hành động
  const getModalContent = () => {
    if (actionType === 'block') {
      return {
        title: t('user:actions.blockUserTitle', 'Khóa tài khoản người dùng'),
        description: t('user:actions.blockUserDescription', {
          userName,
          defaultValue: 'Bạn có chắc chắn muốn khóa tài khoản của {{userName}}?',
        }),
        reasonLabel: t('user:actions.blockReasonLabel', 'Lý do khóa tài khoản'),
        reasonPlaceholder: t('user:actions.blockReasonPlaceholder', 'Nhập lý do khóa tài khoản...'),
        confirmButton: t('user:actions.confirmBlock', 'Khóa tài khoản'),
      };
    } else {
      return {
        title: t('user:actions.unblockUserTitle', 'Mở khóa tài khoản người dùng'),
        description: t('user:actions.unblockUserDescription', {
          userName,
          defaultValue: 'Bạn có chắc chắn muốn mở khóa tài khoản của {{userName}}?',
        }),
        reasonLabel: t('user:actions.unblockReasonLabel', 'Lý do mở khóa tài khoản'),
        reasonPlaceholder: t('user:actions.unblockReasonPlaceholder', 'Nhập lý do mở khóa tài khoản...'),
        confirmButton: t('user:actions.confirmUnblock', 'Mở khóa tài khoản'),
      };
    }
  };

  const modalContent = getModalContent();

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleCancel}
      title={modalContent.title}
      size="md"
    >
      <div className="space-y-4">
        {/* Mô tả */}
        <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
          {modalContent.description}
        </Typography>

        {/* Form */}
        <Form
          ref={formRef}
          onSubmit={handleSubmit}
          defaultValues={{ reason: '' }}
        >
          <FormItem
            label={modalContent.reasonLabel}
            name="reason"
            required
          >
            <Textarea
              placeholder={modalContent.reasonPlaceholder}
              rows={4}
              disabled={isSubmitting}
              fullWidth
            />
          </FormItem>

          {/* Buttons */}
          <div className="flex justify-end space-x-3 mt-6">
            <Button
              type="button"
              variant="secondary"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button
              type="submit"
              variant={actionType === 'block' ? 'danger' : 'primary'}
              isLoading={isSubmitting}
            >
              {modalContent.confirmButton}
            </Button>
          </div>
        </Form>
      </div>
    </Modal>
  );
};

export default UserActionModal;
